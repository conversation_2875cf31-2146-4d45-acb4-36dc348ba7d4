import time, logging, os, json, sys
import requests
import gspread
import pandas as pd
from oauth2client.service_account import ServiceAccountCredentials
# from langchain_community.document_loaders import CSVLoader  # Not needed, using pandas instead
from langchain_google_genai import GoogleGenerativeAI
from langchain_core.prompts import PromptTemplate

# Add src path to sys.path to enable proper imports
src_path = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(src_path)

try:
    from configuration.helpers.config_loader import get_api_config
    api_config = get_api_config()
    PHANTOM_API_KEY = api_config.get('phantom_api_key', "4bc5RrYLCBTapPWDChBZzETZecpneyIJor1e5VxEqaA")
    GEMINI_API_KEY = api_config['gemini_api_key']
    SHEET_URL = api_config.get('linkedin_sheet_url', "https://docs.google.com/spreadsheets/d/16Y2vI689Su3gAifxEFHDRQ_RcmnmrWbRIC_npHuHbUE/edit")
except ImportError:
    # Fallback to direct import or defaults
    try:
        from configuration.constants.app import GEMINI_API_KEY
        PHANTOM_API_KEY = "4bc5RrYLCBTapPWDChBZzETZecpneyIJor1e5VxEqaA"
        SHEET_URL = "https://docs.google.com/spreadsheets/d/16Y2vI689Su3gAifxEFHDRQ_RcmnmrWbRIC_npHuHbUE/edit"
    except ImportError:
        print("Warning: Could not import LinkedIn configuration")
        PHANTOM_API_KEY = "4bc5RrYLCBTapPWDChBZzETZecpneyIJor1e5VxEqaA"
        GEMINI_API_KEY = "AIzaSyDzRAIeFhEW6g8pMJ3w-gJgv726VJj_hwk"
        SHEET_URL = "https://docs.google.com/spreadsheets/d/16Y2vI689Su3gAifxEFHDRQ_RcmnmrWbRIC_npHuHbUE/edit"

PHANTOMS = {
    "company_url_finder": "4886747300437263",
    "employee_export":     "4383143640004809",
    "activity_explorer":   "3016437418660427",
}
HEADERS = {
    "X-Phantombuster-Key-1": PHANTOM_API_KEY,
    "Content-Type": "application/json"
}
DOWNLOAD_DIR = os.path.join(os.getcwd(), "phantom_results")

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
log = logging.getLogger("LinkedInScraper")

# --- Helpers ---
def get_sheet():
    # Try multiple possible credential file locations
    credential_paths = [
        'credentials_linkedin.json',  # Current directory (backward compatibility)
        'client/keys/credentials_linkedin.json',  # New organized location
        os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'client', 'keys', 'credentials_linkedin.json'),  # Relative path
        os.path.join(os.getcwd(), 'client', 'keys', 'credentials_linkedin.json')  # From project root
    ]

    creds_file = None
    for path in credential_paths:
        if os.path.exists(path):
            creds_file = path
            break

    if not creds_file:
        raise FileNotFoundError(f"LinkedIn credentials not found. Tried: {credential_paths}")

    creds = ServiceAccountCredentials.from_json_keyfile_name(
        creds_file,
        ['https://spreadsheets.google.com/feeds','https://www.googleapis.com/auth/drive']
    )
    return gspread.authorize(creds).open_by_url(SHEET_URL).sheet1

def clear_and_write(sheet, company):
    sheet.clear()
    sheet.append_row(["company"])
    sheet.append_row([company.strip()])
    log.info(f"✅ Sheet updated for '{company}'")

def launch_agent(agent_id, payload=None):
    url = f"https://api.phantombuster.com/api/v1/agent/{agent_id}/launch"
    res = requests.post(url, headers=HEADERS, json=payload or {})
    res.raise_for_status()
    cid = res.json().get("data", {}).get("containerId")
    if not cid:
        raise Exception(f"No containerId returned for agent {agent_id}")
    log.info(f"✈ Launched agent {agent_id} (container {cid})")

def download_via_s3(agent_id):
    r = requests.get("https://api.phantombuster.com/api/v2/agents/fetch",
                     headers=HEADERS, params={"id": agent_id})
    r.raise_for_status()
    res = r.json()
    s3 = res.get("s3Folder")
    org = res.get("orgS3Folder")
    if not s3 or not org:
        raise Exception("Missing s3Folder/orgS3Folder—results not ready")
    url = f"https://phantombuster.s3.amazonaws.com/{org}/{s3}/result.csv"
    log.info(f"📥 Downloading CSV from S3: {url}")
    resp = requests.get(url)
    resp.raise_for_status()
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    path = os.path.join(DOWNLOAD_DIR, "latest.csv")
    with open(path, "wb") as f:
        f.write(resp.content)
    log.info("✅ CSV downloaded successfully")
    return path

def download_and_filter(company_term):
    raw_path = download_via_s3(PHANTOMS["activity_explorer"])

    # Ensure the download directory exists
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)

    df = pd.read_csv(raw_path)
    log.info(f"📊 Raw CSV loaded with {len(df)} rows")

    df_filtered = df[df['profileUrl'].str.contains(company_term, case=False, na=False)]
    if df_filtered.empty:
        raise Exception(f"No entries found for '{company_term}' in profileUrl column")

    log.info(f"🔍 Filtered to {len(df_filtered)} rows for '{company_term}'")

    df_filtered['timestamp'] = pd.to_datetime(df_filtered['timestamp'])
    df_latest = df_filtered.sort_values('timestamp', ascending=False).head(5)

    # Use absolute path to avoid any path resolution issues
    filtered_path = os.path.abspath(os.path.join(DOWNLOAD_DIR, "filtered_latest.csv"))
    df_latest.to_csv(filtered_path, index=False)
    log.info(f"✅ Filtered CSV created at: {filtered_path}")

    # Verify the file was created and is readable
    if not os.path.exists(filtered_path):
        raise FileNotFoundError(f"Failed to create filtered CSV at {filtered_path}")

    return filtered_path

def analyze_with_gemini(path, company):
    try:
        # Check if file exists
        if not os.path.exists(path):
            raise FileNotFoundError(f"CSV file not found: {path}")

        # Try to load CSV with pandas first (more reliable)
        import pandas as pd
        df = pd.read_csv(path)

        if df.empty:
            raise ValueError("CSV file is empty")

        # Convert DataFrame to text for analysis
        csv_content = df.to_string(index=False)

        # Use direct text instead of CSVLoader for better reliability
        llm = GoogleGenerativeAI(model="models/gemini-2.0-flash", google_api_key=GEMINI_API_KEY)
        prompt = PromptTemplate(
            input_variables=["context","company"],
            template="""
Analyzing LinkedIn activities for {company} from latest data:
{context}

Summarize:
1. Company updates
2. Product/news
3. Role changes
4. Noteworthy posts
"""
        )
        result = (prompt | llm).invoke({"context": csv_content, "company": company})
        log.info("🎯 Gemini analysis complete")
        return result

    except Exception as e:
        log.error(f"❌ Gemini analysis failed: {e}")
        # Return a fallback summary based on the CSV data
        try:
            import pandas as pd
            df = pd.read_csv(path)
            post_count = len(df)
            return f"LinkedIn analysis for {company}: Found {post_count} recent posts and activities. Data collected successfully but detailed analysis unavailable."
        except:
            return f"LinkedIn data collected for {company} but analysis unavailable."

def delete_all_outputs():
    for pid in PHANTOMS.values():
        requests.delete("https://api.phantombuster.com/api/v2/agents/fetch-output",
                        headers=HEADERS, params={"id": pid})
    log.info("🗑 Cleanup done")

def get_company_linkedin_data(company_name: str):
    """
    Main function to get LinkedIn data for a company
    Returns standardized data format compatible with the main workflow
    """
    print(f"🔗 Processing LinkedIn data for: {company_name}")

    try:
        sheet = get_sheet()
        clear_and_write(sheet, company_name)

        # Launch all Phantoms
        launch_agent(PHANTOMS["company_url_finder"], {"argument": {"spreadsheetUrl": SHEET_URL}})
        launch_agent(PHANTOMS["employee_export"])
        launch_agent(PHANTOMS["activity_explorer"])

        # Start static 2‑minute countdown immediately
        log.info("⏳ Started 2‑minute timer…")
        time.sleep(120)

        # Download, filter, analyze
        result_path = download_and_filter(company_name)
        summary = analyze_with_gemini(result_path, company_name)

        # Clean up
        delete_all_outputs()

        # Convert to standardized format
        linkedin_data = [{
            'headline': f"LinkedIn Activity Summary for {company_name}",
            'description': str(summary)[:200] + '...' if len(str(summary)) > 200 else str(summary),
            'url': SHEET_URL,
            'image_url': '',
            'full_content': str(summary),
            'source': 'linkedin',
            'date': time.strftime('%Y-%m-%d')
        }]

        # Save LinkedIn data to JSON file
        filename = f"linkedin_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(linkedin_data, f, indent=2, ensure_ascii=False)
        print(f"💾 LinkedIn data saved to {filename}")

        print(f"✅ Found LinkedIn data for {company_name}")
        return linkedin_data

    except Exception as e:
        log.error(f"❌ LinkedIn scraping failed for {company_name}: {e}")
        return []

# --- Main Workflow (for standalone testing) ---
if __name__ == "__main__":
    company = input("Enter company name: ").strip()
    result = get_company_linkedin_data(company)
    print("\n🧠 LinkedIn Summary:")
    for item in result:
        print(f"- {item['headline']}")
        print(f"  {item['full_content']}")
