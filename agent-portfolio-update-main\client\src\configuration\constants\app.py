# ============================================================================
# API CONFIGURATION
# ============================================================================

# Gemini API Configuration
GEMINI_API_KEY = 'AIzaSyDzRAIeFhEW6g8pMJ3w-gJgv726VJj_hwk'
GEMINI_API_URL = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}'

# Google Search API Configuration
GOOGLE_SEARCH_API_KEY = "AIzaSyAoifm31L-jHp_zY9C8oElbkZNiHQtlMt4"
GOOGLE_CSE_ID = "e625afe12487c41bd"

# YouTube API Configuration
YOUTUBE_API_KEY = 'AIzaSyBbY40RXm9Ac7Koe662ci25HhYRu6ZuOI4'

# Reddit API Configuration
REDDIT_CLIENT_ID = "yoEOqxqiEGkBb7_WNuLjwA"
REDDIT_CLIENT_SECRET = "Mlsp6pyXsV9UZ82a6v8iMBfplYY99w"
REDDIT_USERNAME = "Federal_Fall_846"
REDDIT_PASSWORD = "Mukilan!23"

# News API Configuration
NEWS_API_KEY = '4f937e60d13441ab90dc10c3006d2a81'

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================

# Common headers for web scraping
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# Date range settings (days back from today)
DEFAULT_DATE_RANGE_DAYS = 30

# Pipeline Configuration
REPORT_STORAGE_FORMAT = "html"
EMBEDDING_DIMENSION = 768  # textembedding-gecko@003 output dimension
MAX_REPORTS_PER_COMPANY_PER_DAY = 1  # Keep only latest report per day

# ============================================================================
# GCP CONFIGURATION FOR MARKET INTELLIGENCE PIPELINE
# ============================================================================

# GCP Project Configuration
GCP_PROJECT_ID = "email-rag-system-459904"  # Update with your project ID
GCP_REGION = "us-central1"  # Default region for Vertex AI services

# Google Cloud Storage Configuration
GCS_BUCKET_NAME = "market-reports-storage"  # Update with your bucket name
GCS_CREDENTIALS_PATH = "client/keys/gcp_storage_credentials.json"

# Vertex AI Configuration
VERTEX_AI_CREDENTIALS_PATH = "client/keys/gcp_vertex_ai_credentials.json"
VERTEX_AI_EMBEDDING_MODEL = "textembedding-gecko@003"
VERTEX_AI_LOCATION = "us-central1"

# Vertex AI Matching Engine Configuration
MATCHING_ENGINE_INDEX_ENDPOINT = "projects/your-project-id-here/locations/us-central1/indexEndpoints/placeholder"
MATCHING_ENGINE_INDEX_ID = "projects/your-project-id-here/locations/us-central1/indexes/placeholder"
MATCHING_ENGINE_DEPLOYED_INDEX_ID = "market-intelligence-index"
