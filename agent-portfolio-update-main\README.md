# 🚀 Agent Portfolio Update - Market Intelligence System

A comprehensive market intelligence and portfolio analysis system that collects data from multiple sources and generates AI-powered reports.

## ✨ Features

### 📊 **Multi-Source Data Collection**
- **Blog Posts** - Company newsrooms and official blogs
- **Google Search** - News articles and web content
- **Reddit** - Community discussions and sentiment
- **YouTube** - Video content and reviews
- **LinkedIn** - Professional activities via PhantomBuster
- **Gmail** - Email communications (optional)

### 🤖 **AI-Powered Analysis**
- **Gemini AI** integration for content processing
- **Sentiment analysis** across all data sources
- **Strategic insights** and portfolio synthesis
- **Professional HTML reports** with comprehensive analysis

### ☁️ **Google Cloud Integration**
- **Cloud Storage** for report archiving
- **Vertex AI** for advanced embeddings
- **Matching Engine** for RAG capabilities
- **Deduplication** management

## 🏗️ Project Structure

```
agent-portfolio-update/
├── client/
│   ├── src/
│   │   ├── service/
│   │   │   ├── agent/           # AI processing services
│   │   │   ├── data/            # Data aggregation
│   │   │   ├── gcp/             # Google Cloud services
│   │   │   ├── pipeline/        # Market intelligence pipeline
│   │   │   └── portfolio/       # Portfolio synthesis
│   │   ├── utility/
│   │   │   └── scrapers/        # Data collection scrapers
│   │   ├── routes/
│   │   │   └── controller/      # Main application controllers
│   │   └── configuration/       # App configuration
│   ├── data/                    # Generated data files
│   ├── reports/                 # Generated reports
│   └── keys/                    # API credentials (not in git)
├── docs/                        # Documentation
└── requirements.txt             # Python dependencies
```

## 🚀 Quick Start

### 1. **Clone Repository**
```bash
git clone https://github.com/Elevation-AI/agent-portfolio-update.git
cd agent-portfolio-update
```

### 2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

### 3. **Configure Credentials**
Place your API credentials in `client/keys/`:
- `credentials.json` - Google OAuth
- `credentials_linkedin.json` - LinkedIn/Google Sheets
- `gcp_storage_credentials.json` - Google Cloud Storage
- `gcp_vertex_ai_credentials.json` - Vertex AI

### 4. **Run Analysis**
```bash
# Interactive portfolio analyzer
python client/src/routes/controller/portfolioAnalyzer.py

# Complete workflow with all sources
python client/src/routes/controller/masterController.py
```

## 📊 Usage Examples

### **Analyze a Single Company**
```python
from routes.controller.portfolioAnalyzer import PortfolioAnalyzer

analyzer = PortfolioAnalyzer()
company_data = analyzer.collect_company_data("Tesla")
report_file, analysis_file = analyzer.generate_reports({"Tesla": company_data})
```

### **Multi-Source Data Collection**
```python
from service.data.dataAggregator import DataAggregator
from utility.scrapers.blogScraper import get_company_blog_data
from utility.scrapers.googleSearch import get_company_google_data

aggregator = DataAggregator()
blog_data = get_company_blog_data("PayPal")
google_data = get_company_google_data("PayPal")

aggregated = aggregator.aggregate_company_data(
    company_name="PayPal",
    blog_data=blog_data,
    google_data=google_data
)
```

## 🔧 Configuration

### **API Keys Required**
- **Google Gemini API** - For AI processing
- **Google Search API** - For web search
- **Reddit API** - For Reddit data
- **YouTube API** - For video content
- **PhantomBuster API** - For LinkedIn data
- **Google Cloud APIs** - For cloud services

### **Environment Setup**
See `docs/SETUP_GUIDE.md` for detailed configuration instructions.

## 📈 Data Sources

| Source | Data Type | Volume | Processing |
|--------|-----------|---------|------------|
| Blog Posts | Company news, updates | 5-10 posts | Content extraction, date parsing |
| Google Search | News articles, web content | 15-20 articles | Full content scraping |
| Reddit | Community discussions | 20 posts | Sentiment analysis |
| YouTube | Video content, reviews | 10-15 videos | Metadata extraction |
| LinkedIn | Professional activities | 5-10 insights | PhantomBuster automation |

## 🎯 Output Formats

### **Portfolio Report** (`portfolio_report_[company].html`)
- Executive summary
- Data source breakdown
- Sentiment analysis
- Key insights and trends

### **Strategic Analysis** (`strategic_analysis_[company]_[timestamp].html`)
- Investment recommendations
- Risk assessment
- Market positioning
- Competitive analysis

### **Raw Data** (`aggregated_[company].json`)
- Complete dataset
- Source attribution
- Metadata and timestamps

## 🔄 Workflow

1. **Data Collection** - Parallel scraping from all sources
2. **Data Aggregation** - Standardization and deduplication
3. **AI Processing** - Gemini analysis and sentiment scoring
4. **Report Generation** - HTML reports with insights
5. **Cloud Storage** - Optional GCP pipeline integration

## 🛠️ Advanced Features

### **Market Intelligence Pipeline**
- Automated report processing
- Vector embeddings for RAG
- Deduplication management
- Cloud storage integration

### **Portfolio Synthesis**
- Multi-company analysis
- Investment insights
- Risk assessment
- Strategic recommendations

## 📚 Documentation

- [Setup Guide](docs/SETUP_GUIDE.md) - Detailed installation instructions
- [GCP Configuration](docs/gcp_credentials_setup.md) - Cloud setup guide
- [API Documentation](docs/README.md) - Technical reference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary to Elevation AI.

## 🆘 Support

For support and questions:
- Create an issue in this repository
- Contact the Elevation AI team

---

**Built with ❤️ by Elevation AI**
