# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Credentials and API Keys
client/keys/
*.json
!requirements.txt
!package.json

# Generated Data Files
client/data/aggregated/
client/data/logs/
client/reports/final/
client/reports/portfolio/
client/reports/strategic/

# Output Files
aggregated_*.json
portfolio_report_*.html
strategic_analysis_*.html
linkedin_*.json
gmail_*.json

# PhantomBuster Results
phantom_results/
*.csv

# Logs
*.log
logs/
pipeline_log.json
deduplication_log.json

# Temporary Files
temp/
tmp/
*.tmp
*.temp

# Google Cloud
gcp_credentials.json
service-account-key.json
token_*.json

# Environment Variables
.env.local
.env.development
.env.test
.env.production

# Node modules (if any)
node_modules/

# Jupyter Notebooks
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Local development
local_test.py
test_*.py
debug_*.py
