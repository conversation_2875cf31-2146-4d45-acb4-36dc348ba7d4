"""
Secure configuration loader using environment variables.
Provides centralized access to all configuration settings from environment variables.
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any

# Load environment variables from .env file
load_dotenv()

class ConfigurationError(Exception):
    """Raised when required configuration is missing or invalid"""
    pass

def get_api_config() -> Dict[str, str]:
    """Get all API configuration settings from environment variables"""
    return {
        'gemini_api_key': os.getenv('GEMINI_API_KEY', ''),
        'gemini_api_url': os.getenv('GEMINI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'),
        'google_search_api_key': os.getenv('GOOGLE_SEARCH_API_KEY', ''),
        'google_cse_id': os.getenv('GOOGLE_CSE_ID', ''),
        'youtube_api_key': os.getenv('YOUTUBE_API_KEY', ''),
        'reddit_client_id': os.getenv('REDDIT_CLIENT_ID', ''),
        'reddit_client_secret': os.getenv('REDDIT_CLIENT_SECRET', ''),
        'reddit_username': os.getenv('REDDIT_USERNAME', ''),
        'reddit_password': os.getenv('REDDIT_PASSWORD', ''),
        'news_api_key': os.getenv('NEWS_API_KEY', ''),
        'phantom_api_key': os.getenv('PHANTOM_API_KEY', ''),
        'linkedin_sheet_url': os.getenv('LINKEDIN_SHEET_URL', '')
    }

def get_gcp_config() -> Dict[str, str]:
    """Get all GCP configuration settings from environment variables"""
    return {
        'project_id': os.getenv('GCP_PROJECT_ID', ''),
        'region': os.getenv('GCP_REGION', 'us-central1'),
        'bucket_name': os.getenv('GCS_BUCKET_NAME', ''),
        'credentials_path': os.getenv('GCS_CREDENTIALS_PATH', ''),
        'vertex_ai_credentials_path': os.getenv('VERTEX_AI_CREDENTIALS_PATH', ''),
        'vertex_ai_embedding_model': os.getenv('VERTEX_AI_EMBEDDING_MODEL', 'textembedding-gecko@003'),
        'vertex_ai_location': os.getenv('VERTEX_AI_LOCATION', 'us-central1'),
        'matching_engine_index_endpoint': os.getenv('MATCHING_ENGINE_INDEX_ENDPOINT', ''),
        'matching_engine_index_id': os.getenv('MATCHING_ENGINE_INDEX_ID', ''),
        'matching_engine_deployed_index_id': os.getenv('MATCHING_ENGINE_DEPLOYED_INDEX_ID', '')
    }

def get_app_settings() -> Dict[str, Any]:
    """Get application settings from environment variables"""
    return {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        'default_date_range_days': int(os.getenv('DEFAULT_DATE_RANGE_DAYS', '30')),
        'report_storage_format': os.getenv('REPORT_STORAGE_FORMAT', 'html'),
        'embedding_dimension': int(os.getenv('EMBEDDING_DIMENSION', '768')),
        'max_reports_per_company_per_day': int(os.getenv('MAX_REPORTS_PER_COMPANY_PER_DAY', '1'))
    }

def validate_required_config() -> bool:
    """Validate that all required configuration settings are present"""
    errors = []
    warnings = []
    
    # Check API configuration
    api_config = get_api_config()
    required_api_keys = ['gemini_api_key', 'google_search_api_key', 'google_cse_id']
    
    for key in required_api_keys:
        if not api_config.get(key):
            errors.append(f"Missing required API key: {key.upper()}")
    
    # Check GCP configuration
    gcp_config = get_gcp_config()
    required_gcp_keys = ['project_id', 'bucket_name']
    
    for key in required_gcp_keys:
        if not gcp_config.get(key):
            errors.append(f"Missing required GCP setting: {key.upper()}")
    
    # Optional configurations that generate warnings
    optional_keys = ['reddit_client_id', 'youtube_api_key', 'news_api_key']
    for key in optional_keys:
        if not api_config.get(key):
            warnings.append(f"Optional API key not configured: {key.upper()}")
    
    if errors:
        print("❌ Configuration Errors:")
        for error in errors:
            print(f"   - {error}")
        print("\nPlease check your .env file and ensure all required values are set.")
        return False
    
    if warnings:
        print("⚠️ Configuration Warnings:")
        for warning in warnings:
            print(f"   - {warning}")
    
    print("✅ Configuration validation passed")
    return True

def get_full_gemini_api_url() -> str:
    """Get the complete Gemini API URL with API key"""
    api_config = get_api_config()
    api_key = api_config['gemini_api_key']
    base_url = api_config['gemini_api_url']
    
    if not api_key:
        raise ConfigurationError("GEMINI_API_KEY is not configured")
    
    if '?' in base_url:
        return f"{base_url}&key={api_key}"
    else:
        return f"{base_url}?key={api_key}"

def get_gcs_report_path(company_name: str, date_str: str, timestamp_str: str) -> str:
    """Generate the GCS path for a report"""
    company_clean = company_name.replace(' ', '_').replace('.', '').lower()
    return f"{company_clean}/{date_str}/{timestamp_str}.html"

def get_gcs_full_url(company_name: str, date_str: str, timestamp_str: str) -> str:
    """Generate the full GCS URL for a report"""
    gcp_config = get_gcp_config()
    bucket_name = gcp_config['bucket_name']
    path = get_gcs_report_path(company_name, date_str, timestamp_str)
    return f"gs://{bucket_name}/{path}"

if __name__ == "__main__":
    print("Configuration Loader Test")
    print("=" * 50)
    
    # Test configuration loading
    try:
        api_config = get_api_config()
        gcp_config = get_gcp_config()
        app_settings = get_app_settings()
        
        print("API Configuration loaded successfully")
        print("GCP Configuration loaded successfully")
        print("App Settings loaded successfully")
        
        # Validate configuration
        is_valid = validate_required_config()
        
        if is_valid:
            print("\n✅ All configurations are valid and ready to use")
        else:
            print("\n❌ Configuration validation failed")
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
