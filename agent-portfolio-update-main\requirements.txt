# Core Python packages
requests>=2.31.0
beautifulsoup4>=4.12.0
vaderSentiment>=3.3.2

# Google APIs and services
google-api-python-client>=2.108.0
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
google-cloud-storage>=2.10.0
google-cloud-aiplatform>=1.38.0

# YouTube transcript API
youtube-transcript-api>=0.6.1

# Reddit API
praw>=7.7.0

# LinkedIn scraping and Google Sheets
gspread>=5.12.0
oauth2client>=4.1.3
pandas>=2.1.0

# LangChain for AI processing
langchain>=0.1.0
langchain-community>=0.0.10
langchain-google-genai>=0.0.5
langchain-core>=0.1.0

# Web scraping and data processing
lxml>=4.9.0
html5lib>=1.1

# Date and time handling
python-dateutil>=2.8.0

# JSON and data handling
jsonschema>=4.19.0

# Logging and utilities
colorama>=0.4.6
tqdm>=4.66.0

# Development and testing (optional)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0