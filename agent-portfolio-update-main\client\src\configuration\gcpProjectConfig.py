#!/usr/bin/env python3
"""
GCP Project Configuration for Market Intelligence Pipeline

This file contains project-specific GCP configuration that needs to be 
customized for your specific GCP project and resources.

IMPORTANT: You need to update these values with your actual GCP project details.
"""

# ============================================================================
# GCP PROJECT DETAILS - UPDATE THESE VALUES
# ============================================================================

# Your GCP Project ID (found in GCP Console)
GCP_PROJECT_ID = "email-rag-system-459904"

# Google Cloud Storage Bucket Name (must be globally unique)
GCS_BUCKET_NAME = "market-reports-storage"

# ============================================================================
# VERTEX AI MATCHING ENGINE CONFIGURATION - UPDATE AFTER SETUP
# ============================================================================

# Vertex AI Matching Engine Index Endpoint
# Format: "projects/{project_id}/locations/{location}/indexEndpoints/{endpoint_id}"
MATCHING_ENGINE_INDEX_ENDPOINT = "projects/your-project-id-here/locations/us-central1/indexEndpoints/placeholder"

# Vertex AI Matching Engine Index ID
# Format: "projects/{project_id}/locations/{location}/indexes/{index_id}"
MATCHING_ENGINE_INDEX_ID = "projects/your-project-id-here/locations/us-central1/indexes/placeholder"

# Deployed Index ID (the ID you assign when deploying the index)
MATCHING_ENGINE_DEPLOYED_INDEX_ID = "market-intelligence-index"

# ============================================================================
# VALIDATION FUNCTIONS
# ============================================================================

def validate_configuration():
    """Validate that all required configuration values are set"""
    errors = []
    warnings = []

    if not GCP_PROJECT_ID or GCP_PROJECT_ID == "your-project-id-here":
        errors.append("GCP_PROJECT_ID must be set to your actual GCP project ID")

    if not GCS_BUCKET_NAME or GCS_BUCKET_NAME == "your-market-intelligence-bucket":
        errors.append("GCS_BUCKET_NAME must be set to your actual bucket name")

    # Make Matching Engine optional for now
    if not MATCHING_ENGINE_INDEX_ENDPOINT or "placeholder" in MATCHING_ENGINE_INDEX_ENDPOINT:
        warnings.append("MATCHING_ENGINE_INDEX_ENDPOINT not configured - vector storage will be simulated")

    if not MATCHING_ENGINE_INDEX_ID or "placeholder" in MATCHING_ENGINE_INDEX_ID:
        warnings.append("MATCHING_ENGINE_INDEX_ID not configured - vector storage will be simulated")

    if errors:
        print("❌ Configuration Errors:")
        for error in errors:
            print(f"   - {error}")
        return False

    if warnings:
        print("⚠️ Configuration Warnings:")
        for warning in warnings:
            print(f"   - {warning}")

    print("✅ GCP Configuration is valid (with warnings)")
    return True

def get_gcs_report_path(company_name: str, date_str: str, timestamp_str: str) -> str:
    """Generate the GCS path for a report"""
    company_clean = company_name.replace(' ', '_').replace('.', '').lower()
    return f"{company_clean}/{date_str}/{timestamp_str}.html"

def get_gcs_full_url(company_name: str, date_str: str, timestamp_str: str) -> str:
    """Generate the full GCS URL for a report"""
    path = get_gcs_report_path(company_name, date_str, timestamp_str)
    return f"gs://{GCS_BUCKET_NAME}/{path}"

if __name__ == "__main__":
    print("GCP Project Configuration")
    print("=" * 50)
    print(f"Project ID: {GCP_PROJECT_ID}")
    print(f"GCS Bucket: {GCS_BUCKET_NAME}")
    print(f"Index Endpoint: {MATCHING_ENGINE_INDEX_ENDPOINT}")
    print(f"Index ID: {MATCHING_ENGINE_INDEX_ID}")
    print(f"Deployed Index ID: {MATCHING_ENGINE_DEPLOYED_INDEX_ID}")
    print("=" * 50)
    validate_configuration()
