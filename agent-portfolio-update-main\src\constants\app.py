# ============================================================================
# API CONFIGURATION - NOW USING ENVIRONMENT VARIABLES
# ============================================================================
#
# IMPORTANT: This file no longer contains hardcoded API keys for security.
# All sensitive configuration is now loaded from environment variables.
# Please use src/configuration/config_loader.py to access configuration values.
#
# To set up your environment:
# 1. Copy .env.template to .env
# 2. Fill in your actual API keys and configuration values
# 3. Use the config_loader functions to access these values in your code
#
# Example usage:
#   from configuration.config_loader import get_api_config
#   api_config = get_api_config()
#   gemini_key = api_config['gemini_api_key']

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================

# Common headers for web scraping
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# Date range settings (days back from today)
DEFAULT_DATE_RANGE_DAYS = 30

# Pipeline Configuration
REPORT_STORAGE_FORMAT = "html"
EMBEDDING_DIMENSION = 768  # textembedding-gecko@003 output dimension
MAX_REPORTS_PER_COMPANY_PER_DAY = 1  # Keep only latest report per day

# ============================================================================
# LEGACY CONSTANTS - DEPRECATED
# ============================================================================
#
# These constants are deprecated and should not be used directly.
# Use src/configuration/config_loader.py functions instead:
#
# - get_api_config() for API configuration
# - get_gcp_config() for GCP configuration
# - get_app_settings() for application settings
#
# This ensures all sensitive data is properly loaded from environment variables.
