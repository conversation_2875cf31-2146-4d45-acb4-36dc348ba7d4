"""
Configuration loader and utility functions for the news summarizer application.
Provides centralized access to all configuration settings.
"""

import os
import sys

# Add the configuration path to sys.path for imports
config_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if config_path not in sys.path:
    sys.path.append(config_path)

try:
    from constants.app import *
    from db import *
except ImportError as e:
    print(f"Warning: Could not import configuration: {e}")

def get_api_config():
    """Get all API configuration settings"""
    return {
        'gemini_api_key': GEMINI_API_KEY,
        'gemini_api_url': GEMINI_API_URL,
        'google_search_api_key': GOOGLE_SEARCH_API_KEY,
        'google_cse_id': GOOGLE_CSE_ID,
        'youtube_api_key': YOUTUBE_API_KEY,
        'reddit_client_id': REDDIT_CLIENT_ID,
        'reddit_client_secret': REDDIT_CLIENT_SECRET,
        'reddit_username': REDDIT_USERNAME,
        'reddit_password': REDDIT_PASSWORD,
        'news_api_key': NEWS_API_KEY
    }

def get_gcp_config():
    """Get all GCP configuration settings"""
    return {
        'project_id': GCP_PROJECT_ID,
        'region': GCP_REGION,
        'bucket_name': GCS_BUCKET_NAME,
        'credentials_path': GCS_CREDENTIALS_PATH,
        'vertex_ai_credentials_path': VERTEX_AI_CREDENTIALS_PATH,
        'vertex_ai_embedding_model': VERTEX_AI_EMBEDDING_MODEL,
        'vertex_ai_location': VERTEX_AI_LOCATION,
        'matching_engine_index_endpoint': MATCHING_ENGINE_INDEX_ENDPOINT,
        'matching_engine_index_id': MATCHING_ENGINE_INDEX_ID,
        'matching_engine_deployed_index_id': MATCHING_ENGINE_DEPLOYED_INDEX_ID
    }

def get_app_settings():
    """Get application settings"""
    return {
        'headers': HEADERS,
        'default_date_range_days': DEFAULT_DATE_RANGE_DAYS,
        'report_storage_format': REPORT_STORAGE_FORMAT,
        'embedding_dimension': EMBEDDING_DIMENSION,
        'max_reports_per_company_per_day': MAX_REPORTS_PER_COMPANY_PER_DAY
    }

def validate_all_config():
    """Validate all configuration settings"""
    print("Validating configuration...")
    
    # Validate GCP configuration
    gcp_valid = validate_configuration()
    
    # Validate API keys
    api_config = get_api_config()
    missing_keys = []
    
    for key, value in api_config.items():
        if not value or value == "your_api_key_here":
            missing_keys.append(key)
    
    if missing_keys:
        print("⚠️ Missing API Keys:")
        for key in missing_keys:
            print(f"   - {key}")
    
    return gcp_valid and len(missing_keys) == 0
