# GCP Credentials Setup Guide

This guide will help you set up the required GCP credentials for the Market Intelligence Pipeline.

## Required Credentials

You need to create **TWO separate service accounts** with different permissions:

### 1. Storage Service Account (`gcp_storage_credentials.json`)
**Purpose**: For Google Cloud Storage operations (uploading/downloading reports)

**Required Permissions**:
- Storage Object Admin
- Storage Bucket Reader

### 2. Vertex AI Service Account (`gcp_vertex_ai_credentials.json`)  
**Purpose**: For Vertex AI embedding generation and Matching Engine operations

**Required Permissions**:
- Vertex AI User
- AI Platform Developer
- Matching Engine Admin (if available)

## Step-by-Step Setup

### Step 1: Create GCP Project (if needed)
1. Go to [GCP Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Note your **Project ID** (you'll need this)

### Step 2: Enable Required APIs
Enable these APIs in your GCP project:
```bash
gcloud services enable storage.googleapis.com
gcloud services enable aiplatform.googleapis.com
gcloud services enable compute.googleapis.com
```

### Step 3: Create Storage Service Account
1. Go to IAM & Admin > Service Accounts
2. Click "Create Service Account"
3. Name: `market-intelligence-storage`
4. Description: `Service account for GCS operations in market intelligence pipeline`
5. Grant roles:
   - Storage Object Admin
   - Storage Bucket Reader
6. Create and download JSON key
7. Save as `gcp_storage_credentials.json` in your project root

### Step 4: Create Vertex AI Service Account  
1. Go to IAM & Admin > Service Accounts
2. Click "Create Service Account"
3. Name: `market-intelligence-vertex-ai`
4. Description: `Service account for Vertex AI operations in market intelligence pipeline`
5. Grant roles:
   - Vertex AI User
   - AI Platform Developer
6. Create and download JSON key
7. Save as `gcp_vertex_ai_credentials.json` in your project root

### Step 5: Create GCS Bucket
1. Go to Cloud Storage > Buckets
2. Click "Create Bucket"
3. Choose a globally unique name (e.g., `your-company-market-intelligence`)
4. Select region (recommend: us-central1)
5. Use default settings for other options

### Step 6: Update Configuration
1. Edit `gcp_project_config.py`:
   - Set `GCP_PROJECT_ID` to your project ID
   - Set `GCS_BUCKET_NAME` to your bucket name

### Step 7: Test Credentials
Run the validation script:
```bash
python gcp_credentials_validator.py
```

## Security Notes

- **Never commit credential files to version control**
- Add `gcp_*_credentials.json` to your `.gitignore`
- Use environment variables in production
- Regularly rotate service account keys
- Follow principle of least privilege

## Troubleshooting

### Common Issues:
1. **Permission Denied**: Check service account has correct roles
2. **API Not Enabled**: Enable required APIs in GCP Console  
3. **Invalid Credentials**: Re-download service account keys
4. **Bucket Access**: Ensure bucket exists and service account has access

### Getting Help:
- Check GCP Console > IAM & Admin > Service Accounts
- Verify API enablement in GCP Console > APIs & Services
- Test with `gcloud auth application-default login` for debugging
