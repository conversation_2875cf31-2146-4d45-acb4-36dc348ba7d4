#!/usr/bin/env python3
"""
Test script for the restructured codebase
Verifies that all modules can be imported and basic functionality works
"""

import os
import sys
import traceback
from datetime import datetime

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_configuration():
    """Test configuration loading"""
    print("🧪 Testing Configuration System...")
    
    try:
        from configuration.config_loader import (
            get_api_config, 
            get_gcp_config, 
            get_app_settings,
            validate_required_config
        )
        
        # Test configuration loading
        api_config = get_api_config()
        gcp_config = get_gcp_config()
        app_settings = get_app_settings()
        
        print("   ✅ Configuration modules imported successfully")
        print(f"   ✅ API config loaded: {len(api_config)} keys")
        print(f"   ✅ GCP config loaded: {len(gcp_config)} keys")
        print(f"   ✅ App settings loaded: {len(app_settings)} keys")
        
        # Test validation
        is_valid = validate_required_config()
        print(f"   ✅ Configuration validation: {'PASSED' if is_valid else 'WARNINGS'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_helpers():
    """Test helper modules"""
    print("\n🧪 Testing Helper Modules...")
    
    try:
        # Test GCP helpers
        from helpers.gcp.storage_helper import GCSStorageManager
        from helpers.gcp.vertex_ai_helper import VertexAIEmbeddingService
        from helpers.gcp.matching_engine_helper import VertexAIMatchingEngineManager
        from helpers.gcp.credentials_validator import validate_gcp_configuration
        
        print("   ✅ GCP helper modules imported successfully")
        
        # Test report helpers
        from helpers.reports.merge_reports import ReportMerger
        from helpers.reports.process_reports import ReportProcessor
        from helpers.reports.report_deduplication_manager import ReportDeduplicationManager
        
        print("   ✅ Report helper modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Helper modules test failed: {e}")
        traceback.print_exc()
        return False

def test_services():
    """Test service modules"""
    print("\n🧪 Testing Service Modules...")
    
    try:
        # Test core services
        from service.agent.agentService import GeminiProcessor
        from service.data.dataAggregator import DataAggregator
        from service.data.dataAggregatorService import DataAggregator as DataAggregatorService
        from service.portfolio.portfolioSynthesizer import PortfolioSynthesizer
        from service.portfolio.portfolioSynthesizerService import PortfolioSynthesizer as PortfolioSynthesizerService
        
        print("   ✅ Core service modules imported successfully")
        
        # Test GCP pipeline
        from service.gcp.market_intelligence_pipeline import MarketIntelligencePipeline
        
        print("   ✅ GCP pipeline service imported successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Service modules test failed: {e}")
        traceback.print_exc()
        return False

def test_utilities():
    """Test utility modules"""
    print("\n🧪 Testing Utility Modules...")
    
    try:
        # Test scrapers
        from utility.scrapers.blog_post import get_company_blog_data
        from utility.scrapers.google_search import get_company_google_data
        from utility.scrapers.reddit_scrap import get_company_reddit_data
        from utility.scrapers.enhanced_youtube_scraping import get_company_youtube_data
        from utility.scrapers.linkedin_scraper import get_company_linkedin_data
        
        print("   ✅ Scraper modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Utility modules test failed: {e}")
        traceback.print_exc()
        return False

def test_routes():
    """Test route modules"""
    print("\n🧪 Testing Route Modules...")
    
    try:
        # Import route modules (but don't run them)
        import routes.agent.agentRoute
        import routes.controller.masterController
        
        print("   ✅ Route modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Route modules test failed: {e}")
        traceback.print_exc()
        return False

def test_constants():
    """Test constants and legacy modules"""
    print("\n🧪 Testing Constants...")

    try:
        import constants.app
        import constants.errors
        import constants.tables

        print("   ✅ Constants modules imported successfully")

        return True

    except Exception as e:
        print(f"   ❌ Constants test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Restructured Codebase")
    print("=" * 60)
    print(f"Test started at: {datetime.now().isoformat()}")
    print(f"Python path: {sys.path[0]}")
    print("=" * 60)
    
    tests = [
        ("Configuration System", test_configuration),
        ("Helper Modules", test_helpers),
        ("Service Modules", test_services),
        ("Utility Modules", test_utilities),
        ("Route Modules", test_routes),
        ("Constants", test_constants)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The restructured codebase is working correctly.")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the error messages above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
