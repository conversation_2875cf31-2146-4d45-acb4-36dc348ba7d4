# ============================================================================
# API CONFIGURATION - REPLACE WITH YOUR ACTUAL VALUES
# ============================================================================

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent

# Google Search API Configuration
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Reddit API Configuration
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USERNAME=your_reddit_username_here
REDDIT_PASSWORD=your_reddit_password_here

# News API Configuration
NEWS_API_KEY=your_news_api_key_here

# PhantomBuster API Configuration
PHANTOM_API_KEY=your_phantom_api_key_here

# LinkedIn Configuration
LINKEDIN_SHEET_URL=your_linkedin_sheet_url_here

# ============================================================================
# GCP CONFIGURATION
# ============================================================================

# GCP Project Configuration
GCP_PROJECT_ID=your_gcp_project_id_here
GCP_REGION=us-central1

# Google Cloud Storage Configuration
GCS_BUCKET_NAME=your_gcs_bucket_name_here
GCS_CREDENTIALS_PATH=path/to/your/gcp_storage_credentials.json

# Vertex AI Configuration
VERTEX_AI_CREDENTIALS_PATH=path/to/your/gcp_vertex_ai_credentials.json
VERTEX_AI_EMBEDDING_MODEL=textembedding-gecko@003
VERTEX_AI_LOCATION=us-central1

# Vertex AI Matching Engine Configuration
MATCHING_ENGINE_INDEX_ENDPOINT=projects/your-project-id/locations/us-central1/indexEndpoints/your-endpoint
MATCHING_ENGINE_INDEX_ID=projects/your-project-id/locations/us-central1/indexes/your-index
MATCHING_ENGINE_DEPLOYED_INDEX_ID=your-deployed-index-id

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================

# Date range settings (days back from today)
DEFAULT_DATE_RANGE_DAYS=30

# Pipeline Configuration
REPORT_STORAGE_FORMAT=html
EMBEDDING_DIMENSION=768
MAX_REPORTS_PER_COMPANY_PER_DAY=1
