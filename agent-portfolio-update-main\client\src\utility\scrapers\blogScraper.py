import requests
from bs4 import BeautifulSoup
import json
import time
import random
import os
import sys

# Add src path to sys.path to enable proper imports
src_path = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.append(src_path)

try:
    from configuration.helpers.config_loader import get_api_config, get_app_settings
    api_config = get_api_config()
    app_settings = get_app_settings()
    GEMINI_API_KEY = api_config['gemini_api_key']
    GEMINI_API_URL = api_config['gemini_api_url']
    HEADERS = app_settings['headers']
except ImportError:
    # Fallback to direct import
    try:
        from configuration.constants.app import GEMINI_API_KEY, GEMINI_API_URL, HEADERS
    except ImportError:
        print("Warning: Could not import blog scraper configuration")
        GEMINI_API_KEY = None
        GEMINI_API_URL = None
        HEADERS = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }


def call_gemini(prompt: str):
    if not GEMINI_API_KEY or not GEMINI_API_URL:
        print("Gemini API not configured")
        return "Unknown"
    
    response = requests.post(GEMINI_API_URL, json={
        "contents": [{"parts": [{"text": prompt}]}]
    })
    response.raise_for_status()
    return response.json()['candidates'][0]['content']['parts'][0]['text']


def find_official_blog_url(company_name: str) -> str:
    """Find official blog URL using common patterns"""
    # Common blog URL patterns
    company_clean = company_name.lower().replace(' ', '').replace('.', '').replace(',', '')

    # Known patterns for major companies
    known_patterns = {
        'paypal': 'https://newsroom.paypal-corp.com/',
        'tesla': 'https://www.tesla.com/blog',
        'apple': 'https://www.apple.com/newsroom/',
        'microsoft': 'https://blogs.microsoft.com/',
        'google': 'https://blog.google/',
        'amazon': 'https://blog.aboutamazon.com/',
        'meta': 'https://about.meta.com/news/',
        'netflix': 'https://about.netflix.com/en/news',
        'spotify': 'https://newsroom.spotify.com/',
        'uber': 'https://www.uber.com/newsroom/',
        'airbnb': 'https://news.airbnb.com/'
    }

    if company_clean in known_patterns:
        return known_patterns[company_clean]

    # Try common patterns
    common_patterns = [
        f"https://blog.{company_clean}.com/",
        f"https://www.{company_clean}.com/blog/",
        f"https://www.{company_clean}.com/news/",
        f"https://newsroom.{company_clean}.com/",
        f"https://{company_clean}.com/blog/",
        f"https://{company_clean}.com/news/"
    ]

    # Test which URL exists
    for url in common_patterns:
        try:
            response = requests.head(url, timeout=5, headers=HEADERS)
            if response.status_code == 200:
                print(f"Found blog URL: {url}")
                return url
        except:
            continue

    # Fallback: try with Gemini if available
    if GEMINI_API_KEY and GEMINI_API_URL:
        try:
            prompt = f"""Find the official blog, news, or press release page URL for {company_name}. Return ONLY the URL. Examples:
https://stripe.com/blog
https://newsroom.paypal-corp.com/
https://www.tesla.com/blog

Company: {company_name}
URL:"""
            url = call_gemini(prompt).strip().split('\n')[0]
            if url.startswith('http'):
                return url
        except Exception as e:
            print(f"Gemini fallback failed: {e}")

    # Final fallback
    return f"https://www.{company_clean}.com/blog/"


def extract_posts(blog_url: str):
    print(f"Scraping: {blog_url}")
    response = requests.get(blog_url, headers=HEADERS, timeout=10)
    soup = BeautifulSoup(response.text, 'html.parser')

    # Collect all links on the page
    links = [a['href'] for a in soup.find_all('a', href=True)]
    full_links = list(set([
        link if link.startswith('http') else blog_url.rstrip('/') + '/' + link.lstrip('/')
        for link in links
    ]))

    # Filter likely article links
    valid_links = []
    for link in full_links:
        link = link.split('#')[0]  # Remove fragment
        if any(x in link.lower() for x in [
            '/about', '/contact', '/privacy', '/terms', '/careers',
            '/cookie', '/support', '/industries', '/resources',
            '/partners', '/page/', '/tag/', '/faq', '/help'
        ]):
            continue
        # More flexible blog post detection
        if any(x in link.lower() for x in ['/blog/', '/news/', '/press/', '/article/', '/post/', '/newsroom/', '/stories/', '/updates/']):
            # Check if it looks like an article (has date or slug pattern)
            if any(x in link.lower() for x in ['-', '_', '/', '2024', '2023', '2022', '2021', '2020']):
                valid_links.append(link)
        # Also check for direct newsroom articles (common pattern)
        elif 'newsroom' in blog_url.lower() and any(x in link.lower() for x in ['-', '_', '2024', '2023', '2022', '2021', '2020']):
            # If we're on a newsroom site, be more inclusive
            if not any(x in link.lower() for x in ['/about', '/contact', '/privacy', '/terms']):
                valid_links.append(link)

    # Remove duplicates and limit to reasonable number
    valid_links = list(set(valid_links))[:10]  # Limit to 10 most recent posts
    print(f"Found {len(valid_links)} potential blog post links")

    posts = []
    for i, url in enumerate(valid_links, 1):
        try:
            print(f"Processing post {i}/{len(valid_links)}: {url}")
            post = extract_post_content(url)
            if post:
                posts.append(post)
                print(f"✅ Successfully extracted: {post['headline'][:50]}...")
            else:
                print(f"⚠️ Skipped (too short or invalid)")
        except Exception as e:
            print(f"❌ Error scraping {url}: {e}")
        time.sleep(random.uniform(1, 2))  # polite scraping

    print(f"Successfully extracted {len(posts)} blog posts")
    return posts


def extract_post_content(url: str):
    print(f"Fetching post: {url}")
    response = requests.get(url, headers=HEADERS, timeout=15)
    soup = BeautifulSoup(response.text, 'html.parser')

    # Try multiple selectors for headline
    headline = None
    for selector in ['h1', 'h2.entry-title', '.post-title', '.article-title', 'title']:
        headline_tag = soup.find(selector)
        if headline_tag:
            headline = headline_tag.get_text(strip=True)
            break

    if not headline:
        headline = "No Title"

    # Try multiple approaches for content extraction
    content = ""

    # Method 1: Look for article/main content areas
    content_areas = soup.find_all(['article', 'main', '.post-content', '.entry-content', '.article-content'])
    if content_areas:
        for area in content_areas:
            paragraphs = area.find_all('p')
            content += '\n'.join([p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True)])

    # Method 2: Fallback to all paragraphs if no content found
    if not content:
        paragraphs = soup.find_all('p')
        content = '\n'.join([p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True)])

    # Skip if content is too short (likely not a real article)
    if len(content) < 100:
        return None

    # Try to extract date from multiple sources
    date = None

    # Method 1: <time> tag
    date_tag = soup.find('time')
    if date_tag:
        date = date_tag.get('datetime') if date_tag.has_attr('datetime') else date_tag.get_text(strip=True)

    # Method 2: Common date selectors
    if not date:
        for selector in ['.date', '.published', '.post-date', '.article-date']:
            date_elem = soup.find(selector)
            if date_elem:
                date = date_elem.get_text(strip=True)
                break

    # Method 3: Extract from URL pattern
    if not date:
        import re
        date_match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', url)
        if date_match:
            date = f"{date_match.group(1)}-{date_match.group(2).zfill(2)}-{date_match.group(3).zfill(2)}"

    if not date:
        # Try to get date from Gemini if available, otherwise use current date
        if GEMINI_API_KEY and GEMINI_API_URL:
            date = get_date_from_llm(headline, content, url)
        else:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

    return {
        "headline": headline,
        "date": date,
        "content": content[:1000],  # Limit content length
        "source_url": url
    }


def get_date_from_llm(title: str, content: str, url: str) -> str:
    print(f"Getting date from Gemini for {url}")
    prompt = f"""You are a news analyst. Given the following blog post, identify the **exact publication date** of the article. If not possible, return "Unknown".

Title: {title}

Content:
{content[:2000]}

URL: {url}

Return only the date in YYYY-MM-DD format. If not found, return "Unknown"."""
    try:
        return call_gemini(prompt).strip()
    except Exception as e:
        print(f"Gemini error for {url}: {e}")
        return "Unknown"


def run_pipeline(company_name: str, save_to_file=False):
    """
    Extract blog posts for a company
    Args:
        company_name: Name of the company
        save_to_file: Whether to save results to JSON file
    Returns:
        List of blog posts
    """
    print(f"Processing blog posts for: {company_name}")
    try:
        blog_url = find_official_blog_url(company_name)
        if not blog_url.startswith('http'):
            print(f"Warning: Invalid blog URL returned for {company_name}: {blog_url}")
            return []

        posts = extract_posts(blog_url)

        if save_to_file:
            filename = f"{company_name.replace(' ', '_')}_blog_posts.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(posts, f, indent=2, ensure_ascii=False)
            print(f"Saved {len(posts)} posts to {filename}")

        print(f"Found {len(posts)} blog posts for {company_name}")
        return posts

    except Exception as e:
        print(f"Error processing blog posts for {company_name}: {e}")
        return []


def get_company_blog_data(company_name: str):
    """
    Main function to get blog data for a company
    Returns standardized data format
    """
    return run_pipeline(company_name, save_to_file=False)


if __name__ == "__main__":
    company = "Cart.com"
    posts = run_pipeline(company, save_to_file=True)
    print(json.dumps(posts[:2], indent=2, ensure_ascii=False))  # show first 2 posts
