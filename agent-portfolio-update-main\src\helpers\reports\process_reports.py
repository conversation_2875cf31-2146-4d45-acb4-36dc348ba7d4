#!/usr/bin/env python3
"""
Process Reports Script
Uploads generated reports to GCS and processes them through the RAG pipeline
"""

try:
    from service.gcp.market_intelligence_pipeline import MarketIntelligencePipeline
except ImportError:
    print("Warning: Could not import MarketIntelligencePipeline")
    MarketIntelligencePipeline = None
import os

class ReportProcessor:
    """Wrapper class for report processing functionality"""

    def __init__(self):
        self.pipeline = None
        if MarketIntelligencePipeline:
            try:
                self.pipeline = MarketIntelligencePipeline()
            except Exception as e:
                print(f"Warning: Could not initialize pipeline: {e}")

    def process_reports(self, reports):
        """Process a list of reports"""
        if not self.pipeline:
            print("Pipeline not available")
            return

        for html_file, company_name in reports:
            if os.path.exists(html_file):
                try:
                    result = self.pipeline.process_report(html_file, company_name)
                    print(f"Processed {html_file}: {'Success' if result.get('success') else 'Failed'}")
                except Exception as e:
                    print(f"Error processing {html_file}: {e}")
            else:
                print(f"File not found: {html_file}")

def main():
    print("🚀 Processing IronPDF Reports to GCS")
    print("=" * 50)

    # Initialize pipeline
    if not MarketIntelligencePipeline:
        print("❌ MarketIntelligencePipeline not available")
        return

    try:
        pipeline = MarketIntelligencePipeline()
    except Exception as e:
        print(f"❌ Could not initialize pipeline: {e}")
        return
    
    # List of reports to process
    reports = [
        ('portfolio_report_ironpdf.html', 'ironpdf'),
        ('strategic_analysis_ironpdf_20250618_201701.html', 'ironpdf')
    ]
    
    for html_file, company_name in reports:
        if os.path.exists(html_file):
            print(f"\n📦 Processing: {html_file}")
            print(f"   Company: {company_name}")
            
            try:
                result = pipeline.process_report(html_file, company_name)
                
                if result['success']:
                    print(f"✅ Success! GCS URL: {result.get('final_gcs_url', 'N/A')}")
                    print(f"   Chunks processed: {result.get('chunks_processed', 0)}")
                    print(f"   Vectors stored: {result.get('vectors_stored', 0)}")
                    print(f"   Duration: {result.get('duration_seconds', 0):.2f} seconds")
                else:
                    print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Exception: {e}")
        else:
            print(f"⚠️ File not found: {html_file}")
    
    print(f"\n📊 Final Statistics:")
    stats = pipeline.get_pipeline_statistics()
    print(f"   Total reports processed: {stats['statistics'].get('total_reports_processed', 0)}")
    print(f"   Successful runs: {stats['statistics'].get('successful_runs', 0)}")
    print(f"   Failed runs: {stats['statistics'].get('failed_runs', 0)}")

if __name__ == "__main__":
    main() 